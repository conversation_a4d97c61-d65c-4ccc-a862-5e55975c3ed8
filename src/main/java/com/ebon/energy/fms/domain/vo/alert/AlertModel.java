// AlertModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertModel {
    @JsonProperty("Id")
    private String id;

    public String getAlertId() {
        return id != null ? id.toString() : null;
    }

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("State")
    private String state;

    @JsonProperty("MonitorCondition")
    private String monitorCondition;

    @JsonProperty("MonitorConditionType")
    private String monitorConditionType;

    @JsonProperty("Severity")
    private int severity;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("CreatedOnUtc")
    private LocalDateTime createdOnUtc;

    @JsonProperty("LastModifiedOnUtc")
    private LocalDateTime lastModifiedOnUtc;
}
