// AlertModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
public class AlertModel {
    @JsonProperty("Id")
    private final String id;

    public String getAlertId() {
        return id != null ? id.toString() : null;
    }

    @JsonProperty("SerialNumber")
    private final String serialNumber;

    @JsonProperty("State")
    private final String state;

    @JsonProperty("MonitorCondition")
    private final String monitorCondition;

    @JsonProperty("MonitorConditionType")
    private final String monitorConditionType;

    @JsonProperty("Severity")
    private int severity;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("CreatedOnUtc")
    private final LocalDateTime createdOnUtc;

    @JsonProperty("LastModifiedOnUtc")
    private final LocalDateTime lastModifiedOnUtc;
}
