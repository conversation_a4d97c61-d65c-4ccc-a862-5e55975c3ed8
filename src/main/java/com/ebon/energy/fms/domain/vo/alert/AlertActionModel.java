// AlertActionModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
public class AlertActionModel {
    @JsonProperty("Id")
    private final String id;

    @JsonProperty("AlertId")
    private final String alertId;

    @JsonProperty("Action")
    private final String action;

    @JsonProperty("Result")
    private final String result;

    @JsonProperty("Details")
    private final String details;

    @JsonProperty("LastModifiedById")
    private final String lastModifiedById;

    @JsonProperty("LastModifiedOnUtc")
    private LocalDateTime lastModifiedOnUtc;

    @JsonProperty("CreadtedById")
    private String creadtedById;

    @JsonProperty("CreatedOnUtc")
    private final LocalDateTime createdOnUtc;
}
