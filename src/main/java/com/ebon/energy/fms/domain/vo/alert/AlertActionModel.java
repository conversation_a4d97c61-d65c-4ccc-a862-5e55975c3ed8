// AlertActionModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertActionModel {
    @JsonProperty("Id")
    private String id;

    @JsonProperty("AlertId")
    private String alertId;

    @JsonProperty("Action")
    private String action;

    @JsonProperty("Result")
    private String result;

    @JsonProperty("Details")
    private String details;

    @JsonProperty("LastModifiedById")
    private String lastModifiedById;

    @JsonProperty("LastModifiedOnUtc")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedOnUtc;

    @JsonProperty("CreatedById")
    private String createdById;

    @JsonProperty("CreatedOnUtc")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOnUtc;
}
