// SeverityCategoryModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SeverityCategoryModel {
    @JsonProperty("TotalSev1")
    private int totalSev1;

    @JsonProperty("TotalSev2")
    private int totalSev2;

    @JsonProperty("TotalSev3")
    private int totalSev3;

    @JsonProperty("NewSev1")
    private int newSev1;

    @JsonProperty("NewSev2")
    private int newSev2;

    @JsonProperty("NewSev3")
    private int newSev3;

    @JsonProperty("ClosedSev1")
    private int closedSev1;

    @JsonProperty("ClosedSev2")
    private int closedSev2;

    @JsonProperty("ClosedSev3")
    private int closedSev3;

    public int getTotalNewAlerts() {
        return newSev1 + newSev2 + newSev3;
    }

    public int getTotalClosedAlerts() {
        return closedSev1 + closedSev2 + closedSev3;
    }

    public int getTotalAlerts() {
        return getTotalNewAlerts() + getTotalClosedAlerts();
    }
}
